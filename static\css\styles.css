/* Base Styles */
:root {
    /* Light Theme (Default) - Clean Modern SaaS Theme */
    --bg-primary: #FDFDFD; /* Pure white/alabaster for main chat panel */
    --bg-secondary: #F8F8F8; /* Slightly off-white for secondary elements */
    --text-primary: #1F2937; /* Dark slate for maximum readability */
    --text-secondary: #6B7280; /* Cool grey for de-emphasis */
    --accent-color: #4DA8DA; /* Blue accent color */
    --accent-hover: #3D98CA; /* Slightly darker blue on hover */
    --border-color: #DADADA; /* Subtle border color */
    --message-user-bg: #F2F7FF; /* Very light blue for user messages */
    --message-bot-bg: #FFFFFF; /* White for bot messages */
    --message-text: #1F2937; /* Dark slate for message text */
    --shadow-color: rgba(0, 0, 0, 0.06); /* Subtle shadow */
    --sidebar-bg: #FAFAF7; /* Soft ivory white for sidebar */
    --sidebar-text: #1F2937; /* Dark slate for sidebar text */
    --sidebar-hover: #F0F0ED; /* Slightly darker ivory for hover */
    --sidebar-border: #EBEBEB; /* Subtle border color */
    --sidebar-item-hover: rgba(0, 0, 0, 0.03); /* Very subtle hover background */
    --sidebar-item-active: #D1E3FF; /* Pale blue tint for active items */
    --sidebar-accent: #4DA8DA; /* Blue accent color for active items */
    --input-bg: #FFFFFF; /* Pure white for input background */
    --input-border: #DADADA; /* Subtle border for input */
    --input-text: #1F2937; /* Dark slate for input text */
    --input-placeholder: #9CA3AF; /* Medium grey for placeholder */
    --welcome-bg: #FDFDFD; /* Match main background */
    --chip-bg: #F2F7FF; /* Very light blue for suggestion chips */
    --chip-text: #1F2937; /* Dark slate for chip text */
    --chip-hover-bg: #4DA8DA; /* Blue for chip hover */
    --chip-hover-text: #FFFFFF; /* White text on hover */
    --button-disabled: #E5E7EB; /* Light grey for disabled buttons */
    --button-disabled-text: #9CA3AF; /* Medium grey for disabled text */
    --header-height: 60px;
    --footer-height: 100px;
    --sidebar-width: 260px; /* Increased from 240px */
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.06); /* Shadow for card-like elements */
}

/* Dark Theme */
.theme-dark {
    --bg-primary: #1E1E1E; /* Dark background for main chat area */
    --bg-secondary: #2B2B2B; /* Slightly lighter for secondary elements */
    --text-primary: #FFFFFF; /* White text for dark mode */
    --text-secondary: #EDEDED; /* Light gray secondary text */
    --accent-color: #999999; /* Medium gray accent for dark mode */
    --accent-hover: #AAAAAA; /* Lighter gray on hover */
    --border-color: #333333; /* Subtle border color */
    --message-user-bg: rgba(255, 255, 255, 0.1); /* Subtle white for user messages */
    --message-bot-bg: rgba(255, 255, 255, 0.05); /* Very subtle white for bot messages */
    --message-text: #FFFFFF; /* Match primary text */
    --shadow-color: rgba(0, 0, 0, 0.3); /* Deeper shadows */
    --sidebar-bg: #2B2B2B; /* Dark gray for sidebar */
    --sidebar-text: #FFFFFF; /* White text for sidebar */
    --sidebar-hover: #383838; /* Slightly lighter gray for hover */
    --sidebar-border: #333333; /* Subtle border color */
    --sidebar-item-hover: rgba(255, 255, 255, 0.05); /* Subtle hover background */
    --sidebar-item-active: rgba(255, 255, 255, 0.1); /* Subtle white for active items */
    --sidebar-accent: #999999; /* Medium gray accent for active items */
    --input-bg: #343541; /* Dark gray for input background */
    --input-border: #444654; /* Slightly lighter border for input */
    --input-text: #FFFFFF; /* White text */
    --input-placeholder: #BBBBBB; /* Light gray placeholder */
    --welcome-bg: #1E1E1E; /* Match primary background */
    --chip-bg: rgba(255, 255, 255, 0.1); /* Subtle white for chips */
    --chip-text: #FFFFFF; /* White text */
    --chip-hover-bg: #666666; /* Medium gray for hover */
    --chip-hover-text: #FFFFFF; /* Pure white on hover */
    --button-disabled: #374151; /* Darker gray for disabled */
    --button-disabled-text: #9CA3AF; /* Medium gray text */
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* Stronger shadow for dark mode */
}

/* Dark mode button styles */
.theme-dark .feedback-btn {
    background-color: #2B2B2B;
    border-color: #333333;
    color: #FFFFFF;
}

.theme-dark .feedback-btn:hover {
    background-color: #383838;
}

.theme-dark .feedback-btn.active {
    background-color: #343541;
    border-color: #444654;
    color: #FFFFFF;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.2s ease, color 0.2s ease;
    height: 100vh;
    overflow: hidden;
    font-size: 1rem;
    line-height: 1.5;
    font-weight: 400; /* Regular weight for body text */
}

/* Typography styles */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700; /* Bold weight for headings */
}

p, li, span, div {
    font-weight: 400; /* Regular weight for body text */
}

.app-container {
    display: flex;
    height: 100vh;
    position: relative;
    overflow: hidden;
}

/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    height: 100vh;
    position: relative;
    transition: all 0.3s ease;
    z-index: 20;
    flex-shrink: 0;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    padding: 16px 14px;
    height: var(--header-height);
    border-bottom: 1px solid var(--sidebar-border);
    position: relative;
}

.sidebar-brand-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-right: 12px;
}

.sidebar-brand-text {
    font-weight: 700;
    font-size: 1rem;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.sidebar-close {
    position: absolute;
    right: 14px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--sidebar-text);
    cursor: pointer;
    font-size: 16px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.sidebar-close:hover {
    background-color: var(--sidebar-item-hover);
}

.sidebar-top {
    padding: 14px;
}

.sidebar-conversations {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
    padding: 0 8px;
}

.sidebar-conversations::-webkit-scrollbar {
    width: 4px;
    background: transparent;
}

.sidebar-conversations::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
}

.sidebar-conversations::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
}

.sidebar-bottom {
    margin-top: auto;
    padding: 14px;
    border-top: 1px solid var(--sidebar-border);
}

.sidebar-item {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    border-radius: 6px;
    cursor: pointer;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    position: relative;
    font-weight: 500;
    border-left: 3px solid transparent;
}

.sidebar-item:hover {
    background-color: var(--sidebar-item-hover);
}

.sidebar-item.active {
    background-color: var(--sidebar-item-active);
    border-left: 3px solid var(--sidebar-accent);
    font-weight: 700;
}

.sidebar-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    margin-right: 12px;
    font-size: 1rem;
}

.sidebar-item-text {
    flex: 1;
    font-size: 0.875rem;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.sidebar-item-toggle {
    margin-left: 8px;
}

.new-chat-btn {
    background-color: transparent;
    color: var(--sidebar-text);
    border: 1px solid var(--sidebar-border);
    padding: 12px 16px;
    border-radius: 6px;
    cursor: pointer;
    width: 100%;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    font-weight: 500;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.new-chat-btn:hover {
    background-color: var(--sidebar-item-hover);
}

.chat-history-list {
    display: flex;
    flex-direction: column;
    gap: 2px;
    padding: 8px 0;
    padding-top: 0;
}

.chat-history-item {
    padding: 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.2s ease;
    color: var(--sidebar-text);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    border-left: 3px solid transparent;
}

.chat-history-item:hover {
    background-color: var(--sidebar-item-hover);
}

.chat-history-item.active {
    background-color: var(--sidebar-item-active);
    border-left: 3px solid var(--sidebar-accent);
    font-weight: 700;
}

.chat-history-header {
    position: sticky;
    top: 0;
    z-index: 2;
    background: var(--sidebar-bg);
    padding: 10px 16px;
    margin-top: 0;
    margin-bottom: 4px;
    border-bottom: 1px solid var(--sidebar-border);
}

.chat-history-item-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}

.chat-history-header + .chat-history-item {
    margin-top: 2px;
}

.upload-status {
    margin-top: 8px;
    font-size: 12px;
    padding: 0 12px;
    color: var(--accent-color);
}

/* Switch Toggle */
.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: .2s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .2s;
}

input:checked + .slider {
    background-color: var(--accent-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--accent-color);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

.slider.round {
    border-radius: 20px;
}

.slider.round:before {
    border-radius: 50%;
}

/* Chat Container Styles */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
    position: relative;
    background-color: var(--bg-primary);
    overflow: hidden;
}

.chat-header {
    height: var(--header-height);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    padding: 0 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: none !important;
    z-index: 10;
}

.sidebar-toggle {
    background: transparent;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    font-size: 16px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0;
    transition: color 0.2s;
    box-shadow: none;
    outline: none;
}

.sidebar-toggle:hover {
    background-color: transparent;
}

.chat-title {
    font-size: 16px;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.header-action-btn {
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    font-size: 16px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.header-action-btn:hover {
    background-color: var(--bg-secondary);
}

/* Chat messages scrolling */
.chat-messages {
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
    padding-bottom: 120px;
}
.chat-messages::-webkit-scrollbar {
    width: 6px;
    background: transparent;
}
.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}
.chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
}
.theme-dark .chat-messages::-webkit-scrollbar {
    background: #444654;
}

.welcome-container {
    padding: 0 !important;
    max-width: 350px !important;
    margin: 24px auto 8px auto !important;
    height: auto !important;
    background: none !important;
    box-shadow: none !important;
    border: none !important;
}

.welcome-message {
    padding: 8px 12px !important;
    max-width: 320px !important;
    border-radius: 4px !important;
    font-size: 0.98rem !important;
    margin: 0 auto !important;
    background: #fff !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.04) !important;
    border: 1px solid #e0e0e0 !important;
}

.welcome-message h2 {
    margin-bottom: 16px;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
}

.welcome-message p {
    margin-bottom: 24px;
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 400;
}

.suggestion-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
}

.suggestion-chip {
    background-color: var(--chip-bg);
    color: var(--chip-text);
    border: 1px solid var(--border-color);
    padding: 10px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400; /* Changed from 500 to 400 for normal text */
    border-left: none; /* Ensure no left border */
}

.suggestion-chip:hover {
    background-color: var(--chip-hover-bg);
    color: var(--chip-hover-text);
    border-color: var(--accent-color);
}

.message {
    width: 100%;
    padding: 24px 0;
    position: relative;
    line-height: 1.6;
}

.message-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 24px;
    font-size: 1rem;
    font-weight: 400;
    color: var(--text-primary);
}

/* Document in message */
.message-document {
    max-width: 800px;
    margin: 0 auto 12px auto;
    padding: 0 24px;
}

.document-container {
    display: flex;
    align-items: center;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px 12px;
    margin-bottom: 8px;
    max-width: 300px;
}

.message-document .document-icon {
    width: 36px;
    height: 36px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.message-document .document-icon i {
    color: white;
    font-size: 16px;
}

.message-document .document-info {
    flex: 1;
    overflow: hidden;
}

.message-document .document-name {
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-primary);
}

.message-document .document-type {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 2px;
}

.user-message {
    background-color: var(--message-user-bg);
    color: var(--message-text);
}

.bot-message {
    background-color: var(--message-bot-bg);
    color: var(--message-text);
}

.bot-message .message-content {
    margin-bottom: 5px;
}

.message-footer {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    font-size: 12px;
    margin-top: 4px;
    color: var(--text-secondary);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 24px;
}

.time-container {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-top: 8px;
}

.message-time {
    opacity: 0.7;
    font-size: 11px;
    text-align: right;
}

.message-feedback {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    margin-top: 8px;
}

.feedback-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0.8;
    font-size: 12px;
    padding: 0;
    margin: 0;
    border-radius: 2px;
    background-color: #f5f5f5;
}

.feedback-btn:hover {
    opacity: 1;
    background-color: #e9e9e9;
}

.feedback-btn.active {
    opacity: 1;
    color: var(--text-primary);
    background-color: #d9d9d9;
    border-color: #b3b3b3;
}

.feedback-btn.copy-btn.active {
    color: var(--text-primary);
    background-color: #d9d9d9;
}

.feedback-btn.thumbs-up.active {
    color: var(--text-primary);
    background-color: #d9d9d9;
}

.feedback-btn.thumbs-down.active {
    color: var(--text-primary);
    background-color: #d9d9d9;
}

.feedback-btn.audio-btn.active {
    color: var(--text-primary);
    background-color: #d9d9d9;
}

.message-sources {
    cursor: pointer;
    color: var(--accent-color);
    text-decoration: underline;
}

.chat-input-container {
    padding: 4px 8px !important;
    max-width: 340px !important;
    border-radius: 6px !important;
    bottom: 12px !important;
    left: 0 !important;
    right: 0 !important;
    margin: 0 auto !important;
    background: #fff !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.04) !important;
    border: 1px solid #e0e0e0 !important;
}

.chat-input-form {
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: 12px;
    box-shadow: none; /* Removed shadow */
    transition: border-color 0.2s, box-shadow 0.2s;
}

.input-wrapper:focus-within {
    border-color: var(--accent-color);
    box-shadow: none; /* Removed shadow */
}

#userInput {
    flex: 1;
    padding: 14px 80px 14px 16px;
    border: none;
    border-radius: 12px;
    outline: none;
    font-size: 1rem;
    background-color: transparent;
    color: var(--input-text);
    resize: none;
    min-height: 24px;
    max-height: 200px;
    overflow-y: auto;
    line-height: 1.5;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400;
}

#userInput::placeholder {
    color: var(--input-placeholder);
}

.input-actions {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
}

.input-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 16px;
    padding: 8px;
    border-radius: 50%;
    transition: color 0.2s, background-color 0.2s;
}

.input-action-btn:hover {
    color: var(--accent-color);
    background-color: rgba(16, 163, 127, 0.1);
}

.send-btn {
    background: none;
    border: none;
    color: var(--accent-color);
    cursor: pointer;
    font-size: 16px;
    padding: 8px;
    border-radius: 50%;
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    transition: background-color 0.2s;
}

.send-btn:disabled {
    color: var(--button-disabled-text);
    cursor: not-allowed;
}

.send-btn:not(:disabled):hover {
    background-color: rgba(16, 163, 127, 0.1);
}

/* Input footer removed as per user request */
.input-footer {
    display: none;
}

/* Input Document Styles */
.input-document {
    display: flex;
    align-items: center;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px 12px;
    margin-bottom: 8px;
    max-width: 100%;
}

.input-document .document-icon {
    width: 36px;
    height: 36px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.input-document .document-icon i {
    color: white;
    font-size: 16px;
}

.input-document .document-info {
    flex: 1;
    overflow: hidden;
}

.input-document .document-name {
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-primary);
}

.input-document .document-type {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 2px;
}

.input-document .document-remove {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    margin-left: 8px;
    transition: all 0.2s ease;
}

.input-document .document-remove:hover {
    color: #ff5252;
    background-color: rgba(255, 82, 82, 0.1);
}

/* Source Panel Styles */
.source-panel {
    position: absolute;
    top: 0;
    right: 0;
    width: 350px;
    height: 100%;
    background-color: var(--bg-secondary);
    border-left: 1px solid var(--border-color);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 15;
    display: flex;
    flex-direction: column;
}

.source-panel.active {
    transform: translateX(0);
}

.source-panel-header {
    height: var(--header-height);
    padding: 0 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.source-panel-header h3 {
    font-size: 16px;
    font-weight: 600;
}

.source-panel-content {
    padding: 16px;
    overflow-y: auto;
    flex: 1;
}

.source-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.source-item:last-child {
    border-bottom: none;
}

.source-title {
    font-weight: 600;
    margin-bottom: 6px;
    color: var(--text-primary);
    font-size: 1rem;
}

.source-file {
    font-size: 0.8125rem;
    color: var(--text-secondary);
    margin-bottom: 6px;
    font-weight: 400;
}

.source-relevance {
    font-size: 0.8125rem;
    color: var(--accent-color);
    font-weight: 500;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 30;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(4px);
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 8px 24px var(--shadow-color);
    overflow: hidden;
}

.modal-header {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

.modal-header h3 {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
}

.modal-body {
    padding: 24px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 400;
}

.modal-footer {
    padding: 16px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-primary);
}

.recording-indicator {
    text-align: center;
    margin-bottom: 24px;
}

.recording-waves {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
}

.recording-waves span {
    display: inline-block;
    width: 4px;
    height: 4px;
    margin: 0 3px;
    background-color: var(--accent-color);
    border-radius: 50%;
    animation: recording-wave 1.5s infinite ease-in-out;
}

.recording-waves span:nth-child(2) {
    animation-delay: 0.2s;
}

.recording-waves span:nth-child(3) {
    animation-delay: 0.4s;
}

.recording-waves span:nth-child(4) {
    animation-delay: 0.6s;
}

.recording-waves span:nth-child(5) {
    animation-delay: 0.8s;
}

@keyframes recording-wave {
    0%, 100% {
        height: 4px;
    }
    50% {
        height: 24px;
    }
}

.recording-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 24px;
}

.record-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: 0.875rem;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.record-btn:hover:not(:disabled) {
    background-color: var(--accent-hover);
    transform: translateY(-1px);
}

.record-btn:disabled {
    background-color: var(--button-disabled);
    color: var(--button-disabled-text);
    cursor: not-allowed;
}

.transcription-result {
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    min-height: 80px;
    margin-bottom: 12px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    position: relative;
}

.transcription-result:empty::before {
    content: attr(placeholder);
    color: var(--input-placeholder);
    position: absolute;
    top: 12px;
    left: 12px;
    opacity: 0.7;
}

.btn {
    padding: 10px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--accent-color);
    color: white;
    border: none;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--accent-hover);
    transform: translateY(-1px);
}

.btn-primary:disabled {
    background-color: var(--button-disabled);
    color: var(--button-disabled-text);
    cursor: not-allowed;
}

.btn-secondary {
    background-color: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-secondary);
}

/* Icon Button */
.icon-button {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.icon-button:hover {
    background-color: var(--bg-secondary);
}

/* Typing Indicator */
.typing-indicator {
    width: 100%;
    padding: 24px 0;
    background-color: var(--message-bot-bg);
    position: relative;
}

.typing-indicator-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    align-items: center;
}

.typing-indicator span {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin: 0 2px;
    background-color: var(--text-secondary);
    border-radius: 50%;
    opacity: 0.6;
    animation: typing 1.4s infinite both;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 80%, 100% {
        opacity: 0.6;
        transform: translateY(0);
    }
    40% {
        opacity: 1;
        transform: translateY(-4px);
    }
}

/* Toast Notification */
.simple-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 250px;
    max-width: 400px;
    transform: translateY(-20px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    border-left: 4px solid var(--accent-color);
}

.simple-toast.show {
    transform: translateY(0);
    opacity: 1;
}

.toast-close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 18px;
    margin-left: 8px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.toast-close-btn:hover {
    color: var(--text-primary);
}

/* Dark theme styles for toast */
.theme-dark .simple-toast {
    background-color: #343541;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100%;
        transform: translateX(-100%);
        width: var(--sidebar-width);
        z-index: 30;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .source-panel {
        width: 100%;
    }

    .welcome-container {
        padding: 20px;
    }

    .welcome-message {
        width: 100%;
        padding: 20px;
    }

    .message-content {
        padding: 0 16px;
    }

    .message-footer {
        padding: 0 16px;
    }

    .simple-toast {
        left: 20px;
        right: 20px;
        max-width: calc(100% - 40px);
    }
}

@media (max-width: 1200px) {
    .welcome-message {
        max-width: 98vw;
    }
}

.virtual-scroll-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: 100%;
    padding: 20px;
    box-sizing: border-box;
}

.virtual-scroll-container .message {
    margin-bottom: 20px;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.3s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading indicator for infinite scroll */
.loading-messages {
    text-align: center;
    padding: 10px;
    color: #666;
    font-size: 14px;
}

.loading-messages::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #666;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    margin-left: 10px;
    vertical-align: middle;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
